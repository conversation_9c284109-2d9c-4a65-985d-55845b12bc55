/**
 * 修复构建后的枚举错误脚本
 * 这个脚本会在构建后运行，修复被错误压缩的枚举引用
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixEnumErrors() {
    console.log('🔧 开始修复枚举错误...');
    
    const distDir = path.join(__dirname, 'dist');
    const assetsDir = path.join(distDir, 'assets');
    
    if (!fs.existsSync(assetsDir)) {
        console.error('❌ assets 目录不存在');
        return;
    }
    
    // 查找主 JavaScript 文件
    const files = fs.readdirSync(assetsDir);
    const mainJsFile = files.find(file => file.startsWith('main-') && file.endsWith('.js'));
    
    if (!mainJsFile) {
        console.error('❌ 找不到主 JavaScript 文件');
        return;
    }
    
    const mainJsPath = path.join(assetsDir, mainJsFile);
    console.log(`📁 找到主文件: ${mainJsFile}`);
    
    try {
        // 读取文件内容
        let content = fs.readFileSync(mainJsPath, 'utf8');
        console.log(`📖 文件大小: ${content.length} 字符`);
        
        // 记录修复前的问题
        const beforeMatches = content.match(/li\.VIE/g);
        console.log(`🔍 发现 li.VIE 引用: ${beforeMatches ? beforeMatches.length : 0} 个`);
        
        // 修复被压缩的枚举引用
        let fixCount = 0;
        
        // 修复 li.VIE -> CollaborationRole.VIEWER
        content = content.replace(/li\.VIE(?!W)/g, () => {
            fixCount++;
            return 'window.CollaborationRole.VIEWER';
        });
        
        // 修复其他可能的压缩问题
        content = content.replace(/li\.EDI/g, () => {
            fixCount++;
            return 'window.CollaborationRole.EDITOR';
        });
        
        content = content.replace(/li\.ADM/g, () => {
            fixCount++;
            return 'window.CollaborationRole.ADMIN';
        });
        
        content = content.replace(/li\.OWN/g, () => {
            fixCount++;
            return 'window.CollaborationRole.OWNER';
        });
        
        // 在文件开头添加枚举定义
        const enumDefinition = `
// 修复枚举定义
window.CollaborationRole = window.CollaborationRole || {
    VIEWER: 'viewer',
    EDITOR: 'editor',
    ADMIN: 'admin',
    OWNER: 'owner'
};

window.Permission = window.Permission || {
    VIEW_SCENE: 'view_scene',
    EDIT_SCENE: 'edit_scene',
    CREATE_ENTITY: 'create_entity',
    UPDATE_ENTITY: 'update_entity',
    DELETE_ENTITY: 'delete_entity',
    ADD_COMPONENT: 'add_component',
    UPDATE_COMPONENT: 'update_component',
    REMOVE_COMPONENT: 'remove_component',
    UPLOAD_ASSET: 'upload_asset',
    SAVE_SCENE: 'save_scene',
    EXPORT_SCENE: 'export_scene',
    IMPORT_SCENE: 'import_scene',
    ASSIGN_ROLES: 'assign_roles',
    MANAGE_PERMISSIONS: 'manage_permissions'
};

// 创建兼容性映射
window.li = window.li || {};
window.li.VIE = window.CollaborationRole.VIEWER;
window.li.VIEWER = window.CollaborationRole.VIEWER;
window.li.EDITOR = window.CollaborationRole.EDITOR;
window.li.ADMIN = window.CollaborationRole.ADMIN;
window.li.OWNER = window.CollaborationRole.OWNER;

console.log('✅ 枚举修复脚本已执行');

`;
        
        content = enumDefinition + content;
        
        // 写回文件
        fs.writeFileSync(mainJsPath, content, 'utf8');
        
        console.log(`✅ 修复完成! 共修复 ${fixCount} 个引用`);
        console.log(`📝 文件已更新: ${mainJsPath}`);
        
        // 验证修复结果
        const afterMatches = content.match(/li\.VIE(?!W)/g);
        console.log(`🔍 修复后剩余 li.VIE 引用: ${afterMatches ? afterMatches.length : 0} 个`);
        
    } catch (error) {
        console.error('❌ 修复过程中出错:', error);
    }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    fixEnumErrors();
}

export { fixEnumErrors };
